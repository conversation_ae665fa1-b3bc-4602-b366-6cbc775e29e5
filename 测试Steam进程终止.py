#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Steam进程终止功能
用于验证修改后的代码是否正常工作
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_steam_termination():
    """测试Steam进程终止功能"""
    print("=" * 50)
    print("Testing Steam Process Termination")
    print("=" * 50)
    
    try:
        # 导入修改后的模块
        from 模块.Steam功能 import Steam功能管理器
        from 模块.网页显示 import _safe_terminate_steam_processes
        
        print("✅ Modules imported successfully")
        
        # 测试Steam功能管理器
        print("\n1. Testing Steam功能管理器...")
        steam_manager = Steam功能管理器()
        
        # 测试检查Steam进程
        print("   - Checking Steam processes...")
        is_running = steam_manager._检查Steam进程()
        print(f"   - Steam running: {is_running}")
        
        # 测试终止Steam进程
        print("   - Testing Steam termination...")
        result = steam_manager.结束Steam()
        print(f"   - Termination result: {result}")
        
        # 测试网页显示模块的安全终止函数
        print("\n2. Testing _safe_terminate_steam_processes...")
        web_result = _safe_terminate_steam_processes()
        print(f"   - Web termination result: {web_result}")

        # 测试网络请求模块的安全终止函数
        print("\n3. Testing _safe_terminate_steam_processes_for_download...")
        from 模块.网络请求 import _safe_terminate_steam_processes_for_download
        download_result = _safe_terminate_steam_processes_for_download()
        print(f"   - Download termination result: {download_result}")

        print("\n✅ All tests completed successfully!")
        print("=" * 50)
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please check if all modules are available")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()

def test_process_safety():
    """测试进程安全性"""
    print("\n" + "=" * 50)
    print("Testing Process Safety")
    print("=" * 50)
    
    try:
        # 测试psutil是否可用
        print("1. Testing psutil availability...")
        try:
            import psutil
            print("   ✅ psutil imported successfully")
            
            # 安全测试进程枚举
            print("   - Testing safe process enumeration...")
            process_count = 0
            error_count = 0
            
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if proc.info and proc.info.get('name'):
                        process_count += 1
                        if process_count >= 10:  # 只测试前10个进程
                            break
                except Exception as e:
                    error_count += 1
                    if error_count >= 5:  # 最多记录5个错误
                        break
            
            print(f"   - Processed {process_count} processes with {error_count} errors")
            print("   ✅ psutil enumeration test completed")
            
        except ImportError:
            print("   ❌ psutil not available")
        except Exception as e:
            print(f"   ❌ psutil test failed: {e}")
        
        # 测试subprocess是否可用
        print("\n2. Testing subprocess availability...")
        try:
            import subprocess
            print("   ✅ subprocess imported successfully")
            
            # 测试tasklist命令
            print("   - Testing tasklist command...")
            result = subprocess.run(
                ['tasklist', '/FI', 'IMAGENAME eq notepad.exe'],
                capture_output=True,
                text=True,
                timeout=5,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            if result.returncode == 0:
                print("   ✅ tasklist command works")
            else:
                print(f"   ⚠️ tasklist returned code: {result.returncode}")
                
        except Exception as e:
            print(f"   ❌ subprocess test failed: {e}")
        
        print("\n✅ Process safety tests completed!")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Safety test error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Steam Process Termination Test Script")
    print("This script tests the modified Steam termination functionality")
    print()
    
    # 运行测试
    test_process_safety()
    test_steam_termination()
    
    print("\nTest completed. Press Enter to exit...")
    input()
