from flask import Blueprint, jsonify, request, render_template_string, render_template, send_file
from .日志 import logger, send_remote_log, get_current_user_code
from .网络请求 import get_game_list, get_game_details, 一键入库, 下载并解压文件
from .数据处理 import process_game_data
from .VDF处理 import 处理解压目录中的VDF文件, VDF解析器, Lua文件生成器
from .配置检测 import 手动配置检测
from .Steam功能 import steam_manager
import os
import subprocess
import winreg
import shutil
import time
import psutil
from datetime import datetime

web_bp = Blueprint('web', __name__)

def _safe_terminate_steam_processes():
    """
    安全终止Steam进程，使用Windows API替代psutil
    """
    user_code = get_current_user_code()

    try:
        send_remote_log('info', 'Attempting to terminate Steam processes safely', {
            'method': 'windows_api_fallback',
            'stage': 'start'
        }, user_code)

        # 方法1: 尝试使用psutil（带严格异常处理）
        try:
            send_remote_log('info', 'Trying psutil method', {
                'method': 'psutil',
                'stage': 'start'
            }, user_code)

            import psutil
            steam_processes = []
            terminated_count = 0

            # 安全地枚举进程
            try:
                for proc in psutil.process_iter(['pid', 'name']):
                    try:
                        if proc.info and proc.info.get('name'):
                            proc_name = proc.info['name'].lower()
                            if 'steam' in proc_name:
                                steam_processes.append(proc)
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess, AttributeError):
                        continue
                    except Exception as e:
                        send_remote_log('warning', 'Process enumeration error', {
                            'error': str(e),
                            'error_type': type(e).__name__
                        }, user_code)
                        continue

            except Exception as e:
                send_remote_log('error', 'Failed to enumerate processes with psutil', {
                    'error': str(e),
                    'error_type': type(e).__name__
                }, user_code)
                raise  # 抛出异常以使用备用方法

            send_remote_log('info', 'Found Steam processes', {
                'count': len(steam_processes),
                'method': 'psutil'
            }, user_code)

            # 终止进程
            for proc in steam_processes:
                try:
                    proc.terminate()
                    terminated_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                except Exception as e:
                    send_remote_log('warning', 'Process termination error', {
                        'error': str(e),
                        'pid': getattr(proc, 'pid', 'unknown')
                    }, user_code)
                    continue

            # 等待并强制杀死
            time.sleep(2)
            for proc in steam_processes:
                try:
                    if proc.is_running():
                        proc.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                except Exception:
                    continue

            send_remote_log('info', 'Steam processes terminated successfully', {
                'method': 'psutil',
                'terminated_count': terminated_count,
                'total_found': len(steam_processes)
            }, user_code)

            return {
                'status': 'success',
                'method': 'psutil',
                'terminated_count': terminated_count,
                'total_found': len(steam_processes)
            }

        except Exception as psutil_error:
            send_remote_log('warning', 'Psutil method failed, trying Windows API', {
                'psutil_error': str(psutil_error),
                'error_type': type(psutil_error).__name__
            }, user_code)

            # 方法2: 使用Windows API (taskkill命令)
            try:
                send_remote_log('info', 'Trying Windows taskkill method', {
                    'method': 'taskkill',
                    'stage': 'start'
                }, user_code)

                import subprocess

                # 使用taskkill命令终止Steam进程
                steam_processes = ['steam.exe', 'steamwebhelper.exe', 'steamservice.exe']
                terminated_count = 0

                for process_name in steam_processes:
                    try:
                        # 先尝试正常终止
                        result = subprocess.run(
                            ['taskkill', '/F', '/IM', process_name],
                            capture_output=True,
                            text=True,
                            timeout=10,
                            creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                        )

                        if result.returncode == 0:
                            terminated_count += 1
                            send_remote_log('info', 'Process terminated', {
                                'process': process_name,
                                'method': 'taskkill'
                            }, user_code)

                    except subprocess.TimeoutExpired:
                        send_remote_log('warning', 'Taskkill timeout', {
                            'process': process_name
                        }, user_code)
                        continue
                    except Exception as e:
                        send_remote_log('warning', 'Taskkill error', {
                            'process': process_name,
                            'error': str(e)
                        }, user_code)
                        continue

                send_remote_log('info', 'Steam processes terminated via taskkill', {
                    'method': 'taskkill',
                    'terminated_count': terminated_count,
                    'total_attempted': len(steam_processes)
                }, user_code)

                return {
                    'status': 'success',
                    'method': 'taskkill',
                    'terminated_count': terminated_count,
                    'total_attempted': len(steam_processes)
                }

            except Exception as taskkill_error:
                send_remote_log('error', 'All Steam termination methods failed', {
                    'psutil_error': str(psutil_error),
                    'taskkill_error': str(taskkill_error)
                }, user_code)

                return {
                    'status': 'partial_failure',
                    'method': 'none',
                    'psutil_error': str(psutil_error),
                    'taskkill_error': str(taskkill_error)
                }

    except Exception as e:
        send_remote_log('error', 'Critical error in Steam termination', {
            'error': str(e),
            'error_type': type(e).__name__
        }, user_code)

        return {
            'status': 'error',
            'method': 'none',
            'error': str(e)
        }

@web_bp.route('/')
def index():
    """网站首页"""
    user_code = get_current_user_code()
    send_remote_log('info', '用户访问首页', {
        'page': 'index',
        'ip': request.remote_addr,
        'user_agent': request.headers.get('User-Agent', '')
    }, user_code)

    from main import INDEX_HTML
    return render_template_string(INDEX_HTML)

@web_bp.route('/管理')
def management():
    """管理页面"""
    user_code = get_current_user_code()
    send_remote_log('info', '用户访问管理页面', {
        'page': 'management',
        'ip': request.remote_addr
    }, user_code)
    return render_template('管理页面.html')

@web_bp.route('/附加功能')
def additional_features():
    """附加功能页面"""
    user_code = get_current_user_code()
    send_remote_log('info', '用户访问附加功能页面', {
        'page': 'additional_features',
        'ip': request.remote_addr
    }, user_code)
    return render_template('附加功能.html')

@web_bp.route('/设置')
def settings():
    """设置页面"""
    user_code = get_current_user_code()
    send_remote_log('info', '用户访问设置页面', {
        'page': 'settings',
        'ip': request.remote_addr
    }, user_code)
    return render_template('设置.html')

@web_bp.route('/api/games', methods=['GET'])
def api_get_games():
    """获取游戏数据API"""
    user_code = get_current_user_code()
    send_remote_log('info', '用户请求获取游戏列表', {
        'api': '/api/games',
        'method': 'GET',
        'ip': request.remote_addr
    }, user_code)

    try:
        raw_data = get_game_list()
        if "error" in raw_data:
            send_remote_log('warning', '获取游戏列表失败', {
                'error': raw_data["error"],
                'api': '/api/games'
            }, user_code)
            return jsonify({"status": "error", "message": raw_data["error"]})

        processed_data = process_game_data(raw_data)
        send_remote_log('info', '成功获取游戏列表', {
            'total_games': len(processed_data),
            'api': '/api/games'
        }, user_code)

        return jsonify({
            "status": "success",
            "data": processed_data,
            "total": len(processed_data)
        })
    except Exception as e:
        send_remote_log('error', '获取游戏列表异常', {
            'error': str(e),
            'api': '/api/games'
        }, user_code)
        return jsonify({
            "status": "error",
            "message": "获取数据失败"
        }), 500

@web_bp.route('/api/game/<app_id>', methods=['GET'])
def api_get_game(app_id):
    """获取单个游戏详情API"""
    user_code = get_current_user_code()
    send_remote_log('info', '用户请求游戏详情', {
        'api': '/api/game',
        'app_id': app_id,
        'method': 'GET',
        'ip': request.remote_addr
    }, user_code)

    try:
        game_data = get_game_details(app_id)
        if "error" in game_data:
            send_remote_log('warning', '获取游戏详情失败', {
                'app_id': app_id,
                'error': game_data["error"]
            }, user_code)
            return jsonify({"status": "error", "message": game_data["error"]})

        send_remote_log('info', '成功获取游戏详情', {
            'app_id': app_id
        }, user_code)

        return jsonify({
            "status": "success",
            "data": game_data
        })
    except Exception as e:
        send_remote_log('error', '获取游戏详情异常', {
            'app_id': app_id,
            'error': str(e)
        }, user_code)
        return jsonify({
            "status": "error",
            "message": "获取游戏详情失败"
        }), 500

@web_bp.route('/api/game/download', methods=['POST'])
def api_game_download():
    """处理游戏一键入库请求"""
    user_code = get_current_user_code()

    try:
        # 获取请求数据
        data = request.json
        游戏名称 = data.get('name')
        游戏类型 = data.get('type')
        app_id = data.get('appId')

        send_remote_log('info', '用户请求游戏下载链接', {
            'api': '/api/game/download',
            'game_name': 游戏名称,
            'game_type': 游戏类型,
            'app_id': app_id,
            'ip': request.remote_addr
        }, user_code)

        # 参数验证
        if not all([游戏名称, 游戏类型, app_id]):
            send_remote_log('warning', '游戏下载请求参数不完整', {
                'missing_params': [k for k, v in {'name': 游戏名称, 'type': 游戏类型, 'appId': app_id}.items() if not v]
            }, user_code)
            return jsonify({"status": "error", "message": "缺少必要参数"})

        # 获取下载链接
        result = 一键入库(游戏名称, 游戏类型, app_id)

        if result["status"] == "success":
            send_remote_log('info', '成功获取游戏下载链接', {
                'game_name': 游戏名称,
                'app_id': app_id,
                'has_download_url': bool(result.get("下载链接"))
            }, user_code)
            return jsonify({
                "status": "success",
                "message": "获取资源成功",
                "url": result["下载链接"]
            })
        else:
            send_remote_log('warning', '获取游戏下载链接失败', {
                'game_name': 游戏名称,
                'app_id': app_id,
                'error': result.get("message", "未知错误")
            }, user_code)
            return jsonify({"status": "error", "message": result["message"]})

    except Exception as e:
        send_remote_log('error', '游戏下载请求异常', {
            'error': str(e),
            'api': '/api/game/download'
        }, user_code)
        return jsonify({"status": "error", "message": "服务器处理异常"})

@web_bp.route('/api/game/extract', methods=['POST'])
def api_game_extract():
    """处理游戏文件下载和解压请求"""
    user_code = get_current_user_code()

    try:
        # 获取请求数据
        data = request.json
        下载链接 = data.get('url')
        文件名 = data.get('filename')

        send_remote_log('info', '用户请求游戏文件下载解压', {
            'api': '/api/game/extract',
            'filename': 文件名,
            'has_url': bool(下载链接),
            'ip': request.remote_addr
        }, user_code)

        # 参数验证
        if not all([下载链接, 文件名]):
            send_remote_log('warning', '游戏解压请求参数不完整', {
                'missing_params': [k for k, v in {'url': 下载链接, 'filename': 文件名}.items() if not v]
            }, user_code)
            return jsonify({"status": "error", "message": "缺少必要参数"})

        # 下载和解压文件
        logger.info(f"处理下载和解压请求: {文件名}")
        result = 下载并解压文件(下载链接, 文件名)

        if result.get("status") == "success":
            send_remote_log('info', '游戏文件下载解压成功', {
                'filename': 文件名,
                'extract_path': result.get('extract_path', '')
            }, user_code)
        else:
            send_remote_log('warning', '游戏文件下载解压失败', {
                'filename': 文件名,
                'error': result.get('message', '未知错误')
            }, user_code)

        return jsonify(result)

    except Exception as e:
        send_remote_log('error', '游戏文件下载解压异常', {
            'error': str(e),
            'api': '/api/game/extract'
        }, user_code)
        #logger.error(f"处理下载解压请求异常: {str(e)}")
        return jsonify({"status": "error", "message": f"服务器处理异常: {str(e)}"})

@web_bp.route('/api/game/process', methods=['POST'])
def api_game_process():
    """统一处理游戏下载、解压流程"""
    user_code = get_current_user_code()

    try:
        # 获取请求数据
        data = request.json
        下载链接 = data.get('url')
        游戏名称 = data.get('name')
        游戏类型 = data.get('type')
        app_id = data.get('appId')

        send_remote_log('info', '用户开始游戏入库流程', {
            'api': '/api/game/process',
            'game_name': 游戏名称,
            'game_type': 游戏类型,
            'app_id': app_id,
            'ip': request.remote_addr
        }, user_code)

        # 参数验证
        if not all([下载链接, 游戏名称, 游戏类型, app_id]):
            send_remote_log('error', '游戏入库流程失败：参数不完整', {
                'missing_params': [k for k, v in {'url': 下载链接, 'name': 游戏名称, 'type': 游戏类型, 'appId': app_id}.items() if not v],
                'failure_stage': '参数验证',
                'error_category': '参数错误'
            }, user_code)
            return jsonify({"status": "error", "message": "缺少必要参数"})

        # 构建文件名
        文件名 = f"{游戏名称}----{游戏类型}----{app_id}"

        # 安全终止Steam进程
        send_remote_log('info', 'Starting Steam process termination', {
            'api': '/api/game/process',
            'game_name': 游戏名称,
            'app_id': app_id,
            'stage': 'before_steam_termination'
        }, user_code)

        steam_termination_result = _safe_terminate_steam_processes()

        send_remote_log('info', 'Steam process termination completed', {
            'api': '/api/game/process',
            'game_name': 游戏名称,
            'app_id': app_id,
            'stage': 'after_steam_termination',
            'result': steam_termination_result
        }, user_code)

        # 下载和解压文件
        result = 下载并解压文件(下载链接, 文件名, app_id)

        if result.get("status") == "success":
            send_remote_log('info', '游戏入库流程完成', {
                'game_name': 游戏名称,
                'app_id': app_id,
                'success': True
            }, user_code)
        else:
            # 详细记录失败原因
            error_message = result.get('message', '未知错误')
            failure_stage = '未知阶段'
            error_category = '处理失败'

            # 根据错误消息判断失败阶段
            if '下载失败' in error_message or '网络' in error_message:
                failure_stage = '文件下载'
                error_category = '网络错误'
            elif '解压失败' in error_message or '7zip' in error_message or '文件处理失败' in error_message:
                failure_stage = '文件解压'
                error_category = '解压错误'
            elif 'VDF' in error_message or 'Lua' in error_message:
                failure_stage = 'VDF处理'
                error_category = 'VDF错误'
            elif '移动失败' in error_message or 'Steam' in error_message:
                failure_stage = '文件移动'
                error_category = '文件系统错误'
            elif '清理文件失败' in error_message:
                failure_stage = '文件清理'
                error_category = '文件系统错误'

            send_remote_log('error', '游戏入库流程失败', {
                'game_name': 游戏名称,
                'app_id': app_id,
                'error_message': error_message,
                'failure_stage': failure_stage,
                'error_category': error_category,
                'result_details': result
            }, user_code)

        return jsonify(result)

    except Exception as e:
        import traceback
        send_remote_log('error', '游戏入库流程异常', {
            'error': str(e),
            'api': '/api/game/process',
            'game_name': data.get('name') if 'data' in locals() else None,
            'app_id': data.get('appId') if 'data' in locals() else None,
            'failure_stage': '系统异常',
            'error_category': '系统错误',
            'traceback': traceback.format_exc()
        }, user_code)
        return jsonify({"status": "error", "message": "服务器处理异常"})

def check_steam_installation():
    """检测Steam是否安装"""
    try:
        # 方法1: 检查注册表
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Valve\Steam") as key:
                install_path = winreg.QueryValueEx(key, "InstallPath")[0]
                steam_exe = os.path.join(install_path, "steam.exe")
                if os.path.exists(steam_exe):
                    return {"installed": True, "path": install_path, "method": "registry"}
        except (FileNotFoundError, OSError):
            pass

        # 方法2: 检查常见安装路径
        common_paths = [
            r"C:\Program Files (x86)\Steam\steam.exe",
            r"C:\Program Files\Steam\steam.exe",
            r"D:\Steam\steam.exe",
            r"E:\Steam\steam.exe",
            r"F:\Steam\steam.exe"
        ]

        for path in common_paths:
            if os.path.exists(path):
                return {"installed": True, "path": os.path.dirname(path), "method": "common_path"}

        # 方法3: 检查PATH环境变量
        steam_path = shutil.which("steam")
        if steam_path:
            return {"installed": True, "path": os.path.dirname(steam_path), "method": "path"}

        # 方法4: 尝试运行steam命令
        try:
            result = subprocess.run(["steam", "--version"], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return {"installed": True, "path": "unknown", "method": "command"}
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass

        return {"installed": False, "path": None, "method": None}

    except Exception as e:
        return {"installed": False, "path": None, "method": None, "error": str(e)}

def check_7zip_installation():
    """检测7zip是否安装"""
    try:
        # 方法1: 检查注册表
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\7-Zip") as key:
                install_path = winreg.QueryValueEx(key, "Path")[0]
                zip_exe = os.path.join(install_path, "7z.exe")
                if os.path.exists(zip_exe):
                    return {"installed": True, "path": install_path, "method": "registry"}
        except (FileNotFoundError, OSError):
            pass

        # 方法2: 检查常见安装路径
        common_paths = [
            r"C:\Program Files\7-Zip\7z.exe",
            r"C:\Program Files (x86)\7-Zip\7z.exe",
            r"D:\7-Zip\7z.exe",
            r"E:\7-Zip\7z.exe"
        ]

        for path in common_paths:
            if os.path.exists(path):
                return {"installed": True, "path": os.path.dirname(path), "method": "common_path"}

        # 方法3: 检查PATH环境变量
        zip_path = shutil.which("7z")
        if zip_path:
            return {"installed": True, "path": os.path.dirname(zip_path), "method": "path"}

        # 方法4: 尝试运行7z命令
        try:
            result = subprocess.run(["7z"], capture_output=True, text=True, timeout=5)
            if "7-Zip" in result.stdout or "7-Zip" in result.stderr:
                return {"installed": True, "path": "unknown", "method": "command"}
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass

        return {"installed": False, "path": None, "method": None}

    except Exception as e:
        return {"installed": False, "path": None, "method": None, "error": str(e)}

@web_bp.route('/api/system/check-environment', methods=['GET'])
def api_check_environment():
    """检查运行环境API"""
    user_code = get_current_user_code()
    send_remote_log('info', '用户检查系统环境', {
        'api': '/api/system/check-environment',
        'ip': request.remote_addr
    }, user_code)

    try:
        # 检测Steam
        steam_status = check_steam_installation()

        # 检测7zip
        zip_status = check_7zip_installation()

        # 检查安装包是否存在
        from .路径工具 import 获取Steam安装包路径, 获取7zip安装包路径
        steam_installer = 获取Steam安装包路径()
        zip_installer = 获取7zip安装包路径()

        result = {
            "status": "success",
            "steam": {
                "installed": steam_status["installed"],
                "path": steam_status.get("path"),
                "method": steam_status.get("method"),
                "installer_available": os.path.exists(steam_installer)
            },
            "7zip": {
                "installed": zip_status["installed"],
                "path": zip_status.get("path"),
                "method": zip_status.get("method"),
                "installer_available": os.path.exists(zip_installer)
            },
            "overall_status": steam_status["installed"] and zip_status["installed"]
        }

        send_remote_log('info', '系统环境检查完成', {
            'steam_installed': steam_status["installed"],
            'zip_installed': zip_status["installed"],
            'overall_status': result["overall_status"]
        }, user_code)

        return jsonify(result)

    except Exception as e:
        send_remote_log('error', '系统环境检查异常', {
            'error': str(e),
            'api': '/api/system/check-environment'
        }, user_code)
        return jsonify({
            "status": "error",
            "message": "环境检测失败"
        }), 500

@web_bp.route('/api/system/download-installer/<software>', methods=['GET'])
def api_download_installer(software):
    """下载安装包API"""
    user_code = get_current_user_code()
    send_remote_log('info', '用户下载安装包', {
        'api': '/api/system/download-installer',
        'software': software,
        'ip': request.remote_addr
    }, user_code)

    try:
        from .路径工具 import 获取Steam安装包路径, 获取7zip安装包路径

        if software == "steam":
            installer_path = 获取Steam安装包路径()
            filename = "Steam安装包.exe"
        elif software == "7zip":
            installer_path = 获取7zip安装包路径()
            filename = "7zip安装包.exe"
        else:
            send_remote_log('warning', '下载安装包请求软件类型未知', {
                'software': software
            }, user_code)
            return jsonify({"status": "error", "message": "未知的软件类型"}), 400

        if not os.path.exists(installer_path):
            send_remote_log('warning', '安装包文件不存在', {
                'software': software,
                'installer_path': installer_path
            }, user_code)
            return jsonify({"status": "error", "message": "安装包文件不存在"}), 404

        send_remote_log('info', '安装包下载成功', {
            'software': software,
            'filename': filename
        }, user_code)

        return send_file(installer_path, as_attachment=True, download_name=filename)

    except Exception as e:
        send_remote_log('error', '下载安装包异常', {
            'software': software,
            'error': str(e)
        }, user_code)
        logger.error(f"下载安装包异常: {str(e)}")
        return jsonify({"status": "error", "message": f"下载失败: {str(e)}"}), 500

@web_bp.route('/api/system/run-installer/<software>', methods=['POST'])
def api_run_installer(software):
    """运行安装包API"""
    try:
        from .路径工具 import 获取Steam安装包路径, 获取7zip安装包路径

        if software == "steam":
            installer_path = 获取Steam安装包路径()
            software_name = "Steam"
        elif software == "7zip" or software == "zip":
            installer_path = 获取7zip安装包路径()
            software_name = "7-Zip"
        else:
            return jsonify({"status": "error", "message": "未知的软件类型"}), 400

        if not os.path.exists(installer_path):
            return jsonify({"status": "error", "message": "安装包文件不存在"}), 404

        # 使用subprocess运行安装包
        try:
            # 在Windows上运行exe文件
            if os.name == 'nt':  # Windows
                subprocess.Popen([installer_path], shell=True)
            else:  # Linux/Mac
                subprocess.Popen(['xdg-open', installer_path])

            return jsonify({
                "status": "success",
                "message": f"{software_name} 安装包已启动，请按照安装向导完成安装"
            })

        except Exception as run_error:
            return jsonify({
                "status": "error",
                "message": "无法启动安装包"
            }), 500

    except Exception as e:
        return jsonify({"status": "error", "message": "操作失败"}), 500

@web_bp.route('/api/vdf/process', methods=['POST'])
def api_vdf_process():
    """处理VDF文件并生成Lua文件API"""
    try:
        # 获取请求数据
        data = request.json
        解压目录路径 = data.get('extract_path')
        app_id = data.get('app_id')

        # 参数验证
        if not all([解压目录路径, app_id]):
            return jsonify({"status": "error", "message": "缺少必要参数"})

        # 检查目录是否存在
        if not os.path.exists(解压目录路径):
            return jsonify({"status": "error", "message": "解压目录不存在"})

        # 处理VDF文件
        result = 处理解压目录中的VDF文件(解压目录路径, app_id)

        return jsonify(result)

    except Exception as e:
        return jsonify({"status": "error", "message": "服务器处理异常"})

@web_bp.route('/api/vdf/parse', methods=['POST'])
def api_vdf_parse():
    """解析单个VDF文件API"""
    try:
        # 获取请求数据
        data = request.json
        vdf_文件路径 = data.get('vdf_file_path')

        # 参数验证
        if not vdf_文件路径:
            return jsonify({"status": "error", "message": "缺少VDF文件路径参数"})

        # 检查文件是否存在
        if not os.path.exists(vdf_文件路径):
            return jsonify({"status": "error", "message": "VDF文件不存在"})

        # 解析VDF文件
        vdf_parser = VDF解析器()
        result = vdf_parser.解析VDF文件(vdf_文件路径)

        return jsonify(result)

    except Exception as e:
        return jsonify({"status": "error", "message": "服务器处理异常"})

@web_bp.route('/api/lua/generate', methods=['POST'])
def api_lua_generate():
    """生成Lua文件API"""
    try:
        # 获取请求数据
        data = request.json
        depots_data = data.get('depots_data')
        app_id = data.get('app_id')
        输出路径 = data.get('output_path')

        # 参数验证
        if not all([depots_data, app_id, 输出路径]):
            return jsonify({"status": "error", "message": "缺少必要参数"})

        # 生成Lua文件
        lua_generator = Lua文件生成器()
        result = lua_generator.生成Lua文件(depots_data, app_id, 输出路径)

        return jsonify(result)

    except Exception as e:
        return jsonify({"status": "error", "message": "服务器处理异常"})

@web_bp.route('/api/vdf/scan', methods=['POST'])
def api_vdf_scan():
    """扫描目录中的VDF文件API"""
    try:
        # 获取请求数据
        data = request.json
        目录路径 = data.get('directory_path')

        # 参数验证
        if not 目录路径:
            return jsonify({"status": "error", "message": "缺少目录路径参数"})

        # 检查目录是否存在
        if not os.path.exists(目录路径):
            return jsonify({"status": "error", "message": "目录不存在"})

        # 扫描VDF文件
        vdf_parser = VDF解析器()
        vdf_files = vdf_parser.扫描目录中的VDF文件(目录路径)

        return jsonify({
            "status": "success",
            "vdf_files": vdf_files,
            "count": len(vdf_files)
        })

    except Exception as e:
        return jsonify({"status": "error", "message": "服务器处理异常"})

@web_bp.route('/api/lua/download/<app_id>', methods=['GET'])
def api_lua_download(app_id):
    """下载生成的Lua文件API"""
    try:
        # 构建Lua文件路径（假设在临时目录中）
        import tempfile
        临时目录 = tempfile.gettempdir()

        # 查找可能的Lua文件路径
        possible_paths = [
            os.path.join(临时目录, f"{app_id}.lua"),
            os.path.join(临时目录, f"*{app_id}*", f"{app_id}.lua")
        ]

        lua_文件路径 = None
        for path_pattern in possible_paths:
            if '*' in path_pattern:
                # 使用glob查找匹配的路径
                import glob
                matches = glob.glob(path_pattern)
                if matches:
                    lua_文件路径 = matches[0]
                    break
            else:
                if os.path.exists(path_pattern):
                    lua_文件路径 = path_pattern
                    break

        if not lua_文件路径 or not os.path.exists(lua_文件路径):
            return jsonify({"status": "error", "message": "Lua文件不存在"}), 404

        return send_file(lua_文件路径, as_attachment=True, download_name=f"{app_id}.lua")

    except Exception as e:
        return jsonify({"status": "error", "message": "下载失败"}), 500

@web_bp.route('/api/system/check-config', methods=['POST'])
def api_check_config():
    """检查配置环境API"""
    try:
        result = 手动配置检测()
        return jsonify(result)
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "配置检测失败",
            "auto_fixed": False,
            "details": {
                "missing_count": 0,
                "steam_path_found": False
            }
        }), 500

@web_bp.route('/api/system/check-steam-network', methods=['GET'])
def api_check_steam_network():
    """检查Steam网络连接API"""
    try:
        from .网络请求 import 检测Steam网络连接
        result = 检测Steam网络连接()
        return jsonify(result)
    except Exception as e:
        return jsonify({
            "status": "error",
            "connected": False,
            "response_time": 0,
            "status_code": 0,
            "message": f"检测失败: {str(e)}"
        }), 500

@web_bp.route('/api/log/error', methods=['POST'])
def api_log_error():
    """接收前端错误日志API"""
    try:
        from .日志 import send_remote_log, get_current_user_code

        # 获取请求数据
        data = request.json
        level = data.get('level', 'error')
        message = data.get('message', '前端错误')
        extra = data.get('extra', {})

        # 添加请求信息
        extra.update({
            'source': 'frontend',
            'ip': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', ''),
            'timestamp': datetime.now().isoformat()
        })

        # 获取用户代码
        user_code = get_current_user_code()

        # 发送到日志服务
        send_remote_log(level, message, extra, user_code, async_send=False)

        return jsonify({"status": "success"})

    except Exception as e:
        # 静默处理异常，避免影响主流程
        return jsonify({"status": "error", "message": str(e)}), 500


@web_bp.route('/api/installed-games', methods=['GET'])
def api_get_installed_games():
    """获取已入库游戏列表API"""
    try:
        from .配置检测 import _check_steam_installation

        # 获取Steam安装路径
        steam_info = _check_steam_installation()
        if not steam_info["installed"] or not steam_info["path"]:
            return jsonify({
                "status": "error",
                "message": "未检测到Steam安装路径"
            })

        steam_path = steam_info["path"]
        stplug_in_dir = os.path.join(steam_path, "config", "stplug-in")

        # 检查目录是否存在
        if not os.path.exists(stplug_in_dir):
            return jsonify({
                "status": "success",
                "games": [],
                "message": "stplug-in目录不存在"
            })

        # 检查是否需要获取游戏数据
        include_game_data = request.args.get('include_game_data', 'true').lower() == 'true'
        game_data_map = {}

        if include_game_data:
            # 获取游戏数据用于匹配
            try:
                logger.info("开始获取游戏数据用于匹配")
                raw_data = get_game_list()
                if "error" not in raw_data:
                    processed_data = process_game_data(raw_data)
                    # 转换字段名从中文到英文
                    game_data_map = {}
                    for game in processed_data:
                        game_data_map[str(game['ID'])] = {
                            'appid': game['ID'],
                            'name': game['名称'],
                            'type': game['类型']
                        }
                    logger.info(f"成功获取 {len(game_data_map)} 个游戏数据")
                else:
                    logger.warning("获取游戏数据失败")
            except Exception as e:
                logger.warning(f"获取游戏数据异常: {str(e)}")

        # 定义要忽略的lua文件列表
        ignored_lua_files = {
            '137379.lua', '189502.lua', '300754.lua', '475030.lua',
            '561036.lua', '575614.lua', '753952.lua', '778458.lua',
            '961456.lua', '963845.lua'
        }

        # 扫描lua文件
        installed_games = []
        for filename in os.listdir(stplug_in_dir):
            if filename.endswith('.lua'):
                # 跳过要忽略的文件
                if filename in ignored_lua_files:
                    logger.debug(f"忽略文件: {filename}")
                    continue

                try:
                    # 提取AppID（文件名去掉.lua后缀）
                    appid = filename[:-4]

                    # 从游戏数据中获取详细信息
                    if appid in game_data_map:
                        game_info = game_data_map[appid]
                        game = {
                            "appid": appid,
                            "name": game_info["name"],
                            "type": game_info["type"]
                        }
                        logger.debug(f"匹配到游戏: {appid} - {game_info['name']}")
                    else:
                        # 如果在游戏数据中找不到，使用默认信息
                        game = {
                            "appid": appid,
                            "name": f"游戏 {appid}",
                            "type": "未知"
                        }
                        if include_game_data:
                            logger.debug(f"未匹配到游戏: {appid}")

                    installed_games.append(game)

                except Exception as e:
                    logger.warning(f"处理文件 {filename} 时出错: {str(e)}")
                    continue

        # 按AppID排序
        installed_games.sort(key=lambda x: int(x["appid"]) if x["appid"].isdigit() else 0)

        logger.info(f"扫描到 {len(installed_games)} 个已入库游戏")

        result = {
            "status": "success",
            "games": installed_games,
            "total": len(installed_games)
        }

        # 如果包含游戏数据，也返回游戏数据缓存
        if include_game_data and game_data_map:
            result["game_data_cache"] = game_data_map

        return jsonify(result)

    except Exception as e:
        logger.error(f"获取已入库游戏列表失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"获取游戏列表失败: {str(e)}"
        }), 500


@web_bp.route('/api/installed-games-simple', methods=['GET'])
def api_get_installed_games_simple():
    """获取已入库游戏简单列表（仅AppID）"""
    try:
        from .配置检测 import _check_steam_installation

        # 获取Steam安装路径
        steam_info = _check_steam_installation()
        if not steam_info["installed"] or not steam_info["path"]:
            return jsonify({
                "status": "error",
                "message": "未检测到Steam安装路径"
            })

        steam_path = steam_info["path"]
        stplug_in_dir = os.path.join(steam_path, "config", "stplug-in")

        # 检查目录是否存在
        if not os.path.exists(stplug_in_dir):
            return jsonify({
                "status": "success",
                "games": []
            })

        # 定义要忽略的lua文件列表
        ignored_lua_files = {
            '137379.lua', '189502.lua', '300754.lua', '475030.lua',
            '561036.lua', '575614.lua', '753952.lua', '778458.lua',
            '961456.lua', '963845.lua'
        }

        # 扫描lua文件，只返回AppID
        installed_games = []
        for filename in os.listdir(stplug_in_dir):
            if filename.endswith('.lua'):
                # 跳过要忽略的文件
                if filename in ignored_lua_files:
                    logger.debug(f"忽略文件: {filename}")
                    continue

                try:
                    appid = filename[:-4]
                    installed_games.append({"appid": appid})
                except Exception as e:
                    logger.warning(f"处理文件 {filename} 时出错: {str(e)}")
                    continue

        # 按AppID排序
        installed_games.sort(key=lambda x: int(x["appid"]) if x["appid"].isdigit() else 0)

        return jsonify({
            "status": "success",
            "games": installed_games
        })

    except Exception as e:
        logger.error(f"获取简单游戏列表失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"获取游戏列表失败: {str(e)}"
        }), 500


@web_bp.route('/api/delete-game', methods=['POST'])
def api_delete_game():
    """删除已入库游戏API"""
    user_code = get_current_user_code()

    try:
        data = request.get_json()
        if not data or 'appid' not in data:
            send_remote_log('warning', '删除游戏请求缺少AppID参数', {
                'api': '/api/delete-game'
            }, user_code)
            return jsonify({
                "status": "error",
                "message": "缺少AppID参数"
            }), 400

        appid = data['appid']

        send_remote_log('info', '用户删除游戏', {
            'api': '/api/delete-game',
            'app_id': appid,
            'ip': request.remote_addr
        }, user_code)

        from .配置检测 import _check_steam_installation

        # 获取Steam安装路径
        steam_info = _check_steam_installation()
        if not steam_info["installed"] or not steam_info["path"]:
            send_remote_log('warning', '删除游戏失败：未检测到Steam安装路径', {
                'app_id': appid
            }, user_code)
            return jsonify({
                "status": "error",
                "message": "未检测到Steam安装路径"
            })

        steam_path = steam_info["path"]
        lua_file_path = os.path.join(steam_path, "config", "stplug-in", f"{appid}.lua")

        # 检查文件是否存在
        if not os.path.exists(lua_file_path):
            send_remote_log('warning', '删除游戏失败：Lua文件不存在', {
                'app_id': appid,
                'lua_file_path': lua_file_path
            }, user_code)
            return jsonify({
                "status": "error",
                "message": "Lua文件不存在"
            })

        # 删除文件
        os.remove(lua_file_path)
        logger.info(f"已删除游戏 {appid} 的Lua文件")

        send_remote_log('info', '游戏删除成功', {
            'app_id': appid,
            'action': 'delete_game'
        }, user_code)

        return jsonify({
            "status": "success",
            "message": f"成功删除游戏 {appid}"
        })

    except Exception as e:
        send_remote_log('error', '删除游戏异常', {
            'app_id': data.get('appid') if 'data' in locals() else 'unknown',
            'error': str(e)
        }, user_code)
        logger.error(f"删除游戏失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"删除失败: {str(e)}"
        }), 500


@web_bp.route('/api/disable-game-update', methods=['POST'])
def api_disable_game_update():
    """禁用游戏更新API"""
    user_code = get_current_user_code()

    try:
        data = request.get_json()
        if not data or 'appid' not in data:
            send_remote_log('warning', '禁用游戏更新请求缺少AppID参数', {
                'api': '/api/disable-game-update'
            }, user_code)
            return jsonify({
                "status": "error",
                "message": "缺少AppID参数"
            }), 400

        appid = data['appid']

        send_remote_log('info', '用户禁用游戏更新', {
            'api': '/api/disable-game-update',
            'app_id': appid,
            'ip': request.remote_addr
        }, user_code)

        from .配置检测 import _check_steam_installation
        import requests
        import re

        # 获取Steam安装路径
        steam_info = _check_steam_installation()
        if not steam_info["installed"] or not steam_info["path"]:
            send_remote_log('warning', '禁用游戏更新失败：未检测到Steam安装路径', {
                'app_id': appid
            }, user_code)
            return jsonify({
                "status": "error",
                "message": "未检测到Steam安装路径"
            })

        steam_path = steam_info["path"]
        lua_file_path = os.path.join(steam_path, "config", "stplug-in", f"{appid}.lua")

        # 检查lua文件是否存在
        if not os.path.exists(lua_file_path):
            send_remote_log('warning', '禁用游戏更新失败：Lua文件不存在', {
                'app_id': appid,
                'lua_file_path': lua_file_path
            }, user_code)
            return jsonify({
                "status": "error",
                "message": "游戏未入库或Lua文件不存在"
            })

        # 获取Steam应用信息
        def get_steam_app_info(app_id):
            """获取Steam应用信息"""
            url = f"https://steamui.com/api/get_appinfo.php?appid={app_id}"
            try:
                response = requests.get(url, timeout=30)
                response.raise_for_status()
                return response.text
            except requests.exceptions.RequestException:
                return None

        # 提取depot和manifest信息
        def extract_depot_manifest_info(text):
            """提取depot和manifest信息"""
            pattern = r'"(\d+)"\s*\{\s*(?:"dlcappid"\s*"\d+"\s*)?(?:"config"[^}]*\}\s*)?(?:"depotfromapp"[^}]*)?(?:"sharedinstall"[^}]*)?(?:"manifests"\s*\{[^}]*"public"\s*\{\s*"gid"\s*"(\d+)")'
            matches = re.findall(pattern, text, re.DOTALL)

            filtered_matches = []
            for depot_id, gid in matches:
                if depot_id.isdigit() and len(depot_id) >= 6:
                    filtered_matches.append((depot_id, gid))

            return filtered_matches

        # 获取应用信息
        response_text = get_steam_app_info(appid)
        if response_text is None:
            send_remote_log('warning', '禁用游戏更新失败：无法获取Steam应用信息', {
                'app_id': appid
            }, user_code)
            return jsonify({
                "status": "error",
                "message": "无法获取Steam应用信息"
            })

        # 提取depot和manifest信息
        depot_manifest_pairs = extract_depot_manifest_info(response_text)

        if not depot_manifest_pairs:
            send_remote_log('warning', '禁用游戏更新失败：未找到有效的depot信息', {
                'app_id': appid
            }, user_code)
            return jsonify({
                "status": "error",
                "message": "未找到有效的depot信息"
            })

        # 生成setManifestid内容
        manifest_lines = []
        for depot_id, gid in depot_manifest_pairs:
            manifest_lines.append(f'setManifestid({depot_id}, "{gid}", 0)')

        # 读取现有lua文件内容
        try:
            with open(lua_file_path, 'r', encoding='utf-8') as f:
                existing_content = f.read()
        except Exception as e:
            send_remote_log('error', '禁用游戏更新失败：读取Lua文件失败', {
                'app_id': appid,
                'error': str(e)
            }, user_code)
            return jsonify({
                "status": "error",
                "message": f"读取Lua文件失败: {str(e)}"
            })

        # 检查是否已经包含setManifestid
        if 'setManifestid' in existing_content:
            send_remote_log('info', '游戏更新已被禁用', {
                'app_id': appid
            }, user_code)
            return jsonify({
                "status": "success",
                "message": "游戏更新已被禁用"
            })

        # 将setManifestid内容添加到lua文件末尾
        try:
            with open(lua_file_path, 'a', encoding='utf-8') as f:
                f.write('\n')
                for line in manifest_lines:
                    f.write(line + '\n')
        except Exception as e:
            send_remote_log('error', '禁用游戏更新失败：写入Lua文件失败', {
                'app_id': appid,
                'error': str(e)
            }, user_code)
            return jsonify({
                "status": "error",
                "message": f"写入Lua文件失败: {str(e)}"
            })

        logger.info(f"已禁用游戏 {appid} 的更新")

        send_remote_log('info', '游戏更新禁用成功', {
            'app_id': appid,
            'action': 'disable_game_update',
            'manifest_count': len(manifest_lines)
        }, user_code)

        return jsonify({
            "status": "success",
            "message": f"成功禁用游戏 {appid} 的更新",
            "manifest_count": len(manifest_lines)
        })

    except Exception as e:
        send_remote_log('error', '禁用游戏更新异常', {
            'app_id': data.get('appid') if 'data' in locals() else 'unknown',
            'error': str(e)
        }, user_code)
        logger.error(f"禁用游戏更新失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"禁用失败: {str(e)}"
        }), 500


@web_bp.route('/api/clear-all-games', methods=['POST'])
def api_clear_all_games():
    """移除全部已入库游戏API"""
    user_code = get_current_user_code()
    send_remote_log('warning', '用户清空全部游戏', {
        'api': '/api/clear-all-games',
        'ip': request.remote_addr,
        'action': 'clear_all_games'
    }, user_code)

    try:
        from .配置检测 import _check_steam_installation

        # 获取Steam安装路径
        steam_info = _check_steam_installation()
        if not steam_info["installed"] or not steam_info["path"]:
            send_remote_log('warning', '清空游戏失败：未检测到Steam安装路径', {
                'action': 'clear_all_games'
            }, user_code)
            return jsonify({
                "status": "error",
                "message": "未检测到Steam安装路径"
            })

        steam_path = steam_info["path"]
        stplug_in_dir = os.path.join(steam_path, "config", "stplug-in")

        # 检查目录是否存在
        if not os.path.exists(stplug_in_dir):
            send_remote_log('info', '清空游戏完成：stplug-in目录不存在', {
                'action': 'clear_all_games',
                'deleted_count': 0
            }, user_code)
            return jsonify({
                "status": "success",
                "deleted_count": 0,
                "message": "stplug-in目录不存在"
            })

        # 扫描并删除所有lua文件
        deleted_count = 0
        deleted_files = []

        for filename in os.listdir(stplug_in_dir):
            if filename.endswith('.lua'):
                try:
                    file_path = os.path.join(stplug_in_dir, filename)
                    os.remove(file_path)
                    deleted_files.append(filename)
                    deleted_count += 1
                except Exception as e:
                    logger.warning(f"删除文件 {filename} 时出错: {str(e)}")
                    continue

        logger.info(f"成功删除 {deleted_count} 个Lua文件")

        send_remote_log('warning', '全部游戏清空完成', {
            'action': 'clear_all_games',
            'deleted_count': deleted_count,
            'deleted_files_count': len(deleted_files)
        }, user_code)

        return jsonify({
            "status": "success",
            "deleted_count": deleted_count,
            "deleted_files": deleted_files,
            "message": f"成功移除 {deleted_count} 个游戏"
        })

    except Exception as e:
        send_remote_log('error', '清空全部游戏异常', {
            'action': 'clear_all_games',
            'error': str(e)
        }, user_code)
        logger.error(f"移除全部游戏失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"移除失败: {str(e)}"
        }), 500


# Steam功能相关API路由
@web_bp.route('/api/steam/start', methods=['POST'])
def api_steam_start():
    """启动Steam API"""
    user_code = get_current_user_code()
    send_remote_log('info', '用户启动Steam', {
        'api': '/api/steam/start',
        'ip': request.remote_addr
    }, user_code)

    try:
        result = steam_manager.启动Steam()

        if result.get("status") == "success":
            send_remote_log('info', 'Steam启动成功', {
                'action': 'steam_start'
            }, user_code)
        else:
            send_remote_log('warning', 'Steam启动失败', {
                'action': 'steam_start',
                'error': result.get('message', '未知错误')
            }, user_code)

        return jsonify(result)
    except Exception as e:
        send_remote_log('error', 'Steam启动异常', {
            'action': 'steam_start',
            'error': str(e)
        }, user_code)
        logger.error(f"启动Steam API异常: {str(e)}")
        return jsonify({"status": "error", "message": "启动Steam失败"}), 500

@web_bp.route('/api/steam/stop', methods=['POST'])
def api_steam_stop():
    """结束Steam API"""
    user_code = get_current_user_code()
    send_remote_log('info', '用户结束Steam', {
        'api': '/api/steam/stop',
        'ip': request.remote_addr
    }, user_code)

    try:
        result = steam_manager.结束Steam()

        if result.get("status") == "success":
            send_remote_log('info', 'Steam结束成功', {
                'action': 'steam_stop'
            }, user_code)
        else:
            send_remote_log('warning', 'Steam结束失败', {
                'action': 'steam_stop',
                'error': result.get('message', '未知错误')
            }, user_code)

        return jsonify(result)
    except Exception as e:
        send_remote_log('error', 'Steam结束异常', {
            'action': 'steam_stop',
            'error': str(e)
        }, user_code)
        logger.error(f"结束Steam API异常: {str(e)}")
        return jsonify({"status": "error", "message": "结束Steam失败"}), 500

@web_bp.route('/api/steam/install', methods=['POST'])
def api_steam_install():
    """安装Steam API"""
    user_code = get_current_user_code()
    send_remote_log('info', '用户安装Steam', {
        'api': '/api/steam/install',
        'ip': request.remote_addr
    }, user_code)

    try:
        result = steam_manager.安装Steam()

        if result.get("status") == "success":
            send_remote_log('info', 'Steam安装成功', {
                'action': 'steam_install'
            }, user_code)
        else:
            send_remote_log('warning', 'Steam安装失败', {
                'action': 'steam_install',
                'error': result.get('message', '未知错误')
            }, user_code)

        return jsonify(result)
    except Exception as e:
        send_remote_log('error', 'Steam安装异常', {
            'action': 'steam_install',
            'error': str(e)
        }, user_code)
        logger.error(f"安装Steam API异常: {str(e)}")
        return jsonify({"status": "error", "message": "安装Steam失败"}), 500

@web_bp.route('/api/steam/open-website', methods=['POST'])
def api_steam_open_website():
    """打开Steam官网 API"""
    try:
        result = steam_manager.打开Steam官网()
        return jsonify(result)
    except Exception as e:
        logger.error(f"打开Steam官网API异常: {str(e)}")
        return jsonify({"status": "error", "message": "打开Steam官网失败"}), 500

@web_bp.route('/api/steam/uninstall', methods=['POST'])
def api_steam_uninstall():
    """彻底卸载Steam API"""
    try:
        result = steam_manager.彻底卸载Steam()
        return jsonify(result)
    except Exception as e:
        logger.error(f"彻底卸载Steam API异常: {str(e)}")
        return jsonify({"status": "error", "message": "彻底卸载Steam失败"}), 500

@web_bp.route('/api/steam/open-config', methods=['POST'])
def api_steam_open_config():
    """打开Steam配置目录 API"""
    try:
        result = steam_manager.打开Steam配置目录()
        return jsonify(result)
    except Exception as e:
        logger.error(f"打开Steam配置目录API异常: {str(e)}")
        return jsonify({"status": "error", "message": "打开Steam配置目录失败"}), 500

@web_bp.route('/api/steam/restore', methods=['POST'])
def api_steam_restore():
    """系统维护API"""
    user_code = get_current_user_code()
    send_remote_log('info', '用户执行系统维护', {
        'api': '/api/steam/restore',
        'ip': request.remote_addr
    }, user_code)

    try:
        result = steam_manager.一键还原Steam()
        return jsonify(result)
    except Exception as e:
        send_remote_log('error', '系统维护API异常', {
            'action': 'system_maintenance'
        }, user_code)
        return jsonify({"status": "error", "message": "操作失败"}), 500





@web_bp.route('/api/log-user-action', methods=['POST'])
def api_log_user_action():
    """记录用户操作日志API"""
    user_code = get_current_user_code()

    try:
        data = request.json
        action = data.get('action', 'unknown_action')
        extra_data = data.get('extra', {})

        # 添加请求相关信息
        extra_data.update({
            'ip': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', ''),
            'referer': request.headers.get('Referer', ''),
            'api': '/api/log-user-action'
        })

        # 发送日志到远程服务
        send_remote_log('info', f'用户操作: {action}', extra_data, user_code)

        return jsonify({"status": "success"})

    except Exception as e:
        # 静默失败，不影响用户体验
        return jsonify({"status": "error", "message": str(e)})

@web_bp.route('/api/batch-install', methods=['POST'])
def api_batch_install():
    """批量入库热门游戏API"""
    user_code = get_current_user_code()

    # 记录批量入库API调用日志
    send_remote_log('info', '用户调用批量入库API', {
        'api': '/api/batch-install',
        'method': 'POST',
        'ip': request.remote_addr
    }, user_code)

    try:
        from .配置检测 import _check_steam_installation
        import requests
        import os

        # 检查Steam是否安装
        steam_info = _check_steam_installation()
        if not steam_info["installed"] or not steam_info["path"]:
            send_remote_log('warning', '批量入库失败: Steam未安装', {
                'error_type': 'steam_not_found'
            }, user_code)
            return jsonify({
                "status": "error",
                "message": "未检测到Steam安装路径"
            })

        # 构建stplug-in目录路径
        stplug_in_dir = os.path.join(steam_info["path"], "config", "stplug-in")

        # 确保目录存在
        if not os.path.exists(stplug_in_dir):
            try:
                os.makedirs(stplug_in_dir, exist_ok=True)
            except Exception as e:
                return jsonify({
                    "status": "error",
                    "message": f"无法创建目录: {str(e)}"
                })

        # 要下载的文件列表
        file_urls = [
            "http://**************:9697//XIAOBAI/137379.lua",
            "http://**************:9697//XIAOBAI/189502.lua",
            "http://**************:9697//XIAOBAI/300754.lua",
            "http://**************:9697//XIAOBAI/475030.lua",
            "http://**************:9697//XIAOBAI/561036.lua",
            "http://**************:9697//XIAOBAI/575614.lua",
            "http://**************:9697//XIAOBAI/753952.lua",
            "http://**************:9697//XIAOBAI/778458.lua",
            "http://**************:9697//XIAOBAI/961456.lua",
            "http://**************:9697//XIAOBAI/963845.lua"
        ]

        success_count = 0
        failed_files = []

        # 下载每个文件
        for url in file_urls:
            try:
                # 从URL中提取文件名
                filename = url.split('/')[-1]
                file_path = os.path.join(stplug_in_dir, filename)

                # 下载文件
                response = requests.get(url, timeout=30)
                response.raise_for_status()

                # 保存文件
                with open(file_path, 'wb') as f:
                    f.write(response.content)

                success_count += 1

            except Exception as e:
                failed_files.append(filename)
                continue

        if success_count > 0:
            # 记录批量入库成功日志
            send_remote_log('info', '批量入库操作完成', {
                'operation': 'batch_install',
                'success_count': success_count,
                'failed_count': len(failed_files),
                'total_files': len(file_urls),
                'failed_files': failed_files,
                'steam_path': steam_info["path"],
                'result': 'success'
            }, user_code)

            return jsonify({
                "status": "success",
                "message": f"批量入库完成，成功下载 {success_count} 个文件",
                "count": success_count,
                "failed": failed_files
            })
        else:
            # 记录批量入库全部失败日志
            send_remote_log('error', '批量入库操作全部失败', {
                'operation': 'batch_install',
                'success_count': 0,
                'failed_count': len(failed_files),
                'total_files': len(file_urls),
                'failed_files': failed_files,
                'result': 'all_failed'
            }, user_code)

            return jsonify({
                "status": "error",
                "message": "所有文件下载失败",
                "failed": failed_files
            })

    except Exception as e:
        # 记录批量入库异常日志
        send_remote_log('error', '批量入库操作异常', {
            'operation': 'batch_install',
            'error_message': str(e),
            'error_type': type(e).__name__,
            'result': 'exception'
        }, user_code)

        return jsonify({
            "status": "error",
            "message": f"批量入库失败: {str(e)}"
        })

@web_bp.route('/api/check-vip-status', methods=['POST'])
def api_check_vip_status():
    """检查用户VIP状态API"""
    user_code = get_current_user_code()

    try:
        # 获取网络验证客户端
        from main import auth_manager
        auth_client = auth_manager.get_client()

        if not auth_client or not auth_client.is_authenticated():
            return jsonify({
                "status": "error",
                "message": "用户未登录",
                "is_vip": False
            }), 401

        # 获取云数据
        try:
            cloud_data = auth_client.get_cloud_data()

            # 检查云数据中是否包含"超级会员"标识
            is_vip = "超级会员" in cloud_data if cloud_data else False

            send_remote_log('info', '用户检查VIP状态', {
                'api': '/api/check-vip-status',
                'is_vip': is_vip,
                'ip': request.remote_addr
            }, user_code)

            return jsonify({
                "status": "success",
                "is_vip": is_vip,
                "message": "VIP状态检查完成"
            })

        except Exception as e:
            send_remote_log('error', '获取云数据失败', {
                'api': '/api/check-vip-status',
                'error': str(e)
            }, user_code)

            return jsonify({
                "status": "error",
                "message": "无法获取用户状态",
                "is_vip": False
            }), 500

    except Exception as e:
        send_remote_log('error', 'VIP状态检查API异常', {
            'api': '/api/check-vip-status',
            'error': str(e)
        }, user_code)

        return jsonify({
            "status": "error",
            "message": "服务器处理异常",
            "is_vip": False
        }), 500

@web_bp.route('/api/report-error', methods=['POST'])
def api_report_error():
    """错误报告代理API"""
    try:
        import requests

        # 获取前端发送的数据
        request_data = request.get_json()
        if not request_data or 'data' not in request_data:
            return jsonify({
                'status': 'error',
                'message': '缺少必要的数据'
            }), 400

        report_data = request_data['data']

        # 准备发送到目标服务器的数据 - 同时发送中文和英文字段名
        form_data = {
            '数据': report_data,
            'data': report_data
        }

        # 发送到目标服务器
        target_url = 'http://**************:9697/baogao.php'

        response = requests.post(
            target_url,
            data=form_data,
            timeout=10,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Charset': 'UTF-8,*;q=0.5'
            }
        )

        # 获取原始字节内容并尝试解码
        raw_content = response.content
        response_text = ""

        # 常见的中文编码列表
        encodings_to_try = ['gbk', 'gb2312', 'gb18030', 'utf-8', 'big5', 'latin1']

        for encoding in encodings_to_try:
            try:
                response_text = raw_content.decode(encoding)
                # 检查解码后的文本是否包含乱码字符
                if '�' not in response_text and len(response_text.strip()) > 0:
                    break
            except (UnicodeDecodeError, LookupError):
                continue

        # 如果所有编码都失败，使用最宽松的方式
        if not response_text or '�' in response_text:
            try:
                response_text = raw_content.decode('utf-8', errors='replace')
            except Exception:
                response_text = str(raw_content)

        if response.status_code == 200:
            return jsonify({
                'status': 'success',
                'message': '错误报告提交成功'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': f'目标服务器响应错误: {response.status_code}'
            }), 500

    except requests.exceptions.Timeout:
        return jsonify({
            'status': 'error',
            'message': '请求超时，请稍后重试'
        }), 500
    except requests.exceptions.ConnectionError:
        return jsonify({
            'status': 'error',
            'message': '无法连接到服务器'
        }), 500
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'提交失败: {str(e)}'
        }), 500


