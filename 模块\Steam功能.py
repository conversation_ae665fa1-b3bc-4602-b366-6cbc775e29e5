import os
import subprocess
import winreg
import shutil
import webbrowser
import psutil
from .日志 import logger, send_remote_log, get_current_user_code
from .配置检测 import _check_steam_installation
from .路径工具 import 获取安装包目录路径, 获取Steam安装包路径, 获取Geek卸载器路径


class Steam功能管理器:
    """Steam功能管理器"""
    
    def __init__(self):
        self.steam_path = None
        self.安装包目录 = 获取安装包目录路径()
    
    def 获取Steam路径(self):
        """获取Steam安装路径"""
        try:
            steam_info = _check_steam_installation()
            if steam_info["installed"] and steam_info["path"]:
                self.steam_path = steam_info["path"]
                return True
            return False
        except Exception as e:
            logger.error(f"获取Steam路径失败: {str(e)}")
            return False
    
    def 启动Steam(self):
        """启动Steam"""
        user_code = get_current_user_code()
        send_remote_log('info', '尝试启动Steam', {
            'function': '启动Steam',
            'steam_path': self.steam_path
        }, user_code)

        try:
            if not self.获取Steam路径():
                send_remote_log('warning', 'Steam启动失败：未检测到安装路径', {
                    'function': '启动Steam'
                }, user_code)
                return {"status": "error", "message": "未检测到Steam安装路径"}

            steam_exe = os.path.join(self.steam_path, "steam.exe")
            if not os.path.exists(steam_exe):
                send_remote_log('warning', 'Steam启动失败：可执行文件不存在', {
                    'function': '启动Steam',
                    'steam_exe_path': steam_exe
                }, user_code)
                return {"status": "error", "message": "Steam.exe文件不存在"}

            # 检查Steam是否已经在运行
            if self._检查Steam进程():
                send_remote_log('info', 'Steam已在运行中', {
                    'function': '启动Steam',
                    'status': 'already_running'
                }, user_code)
                return {"status": "warning", "message": "Steam已经在运行中"}

            # 启动Steam
            subprocess.Popen([steam_exe], cwd=self.steam_path)
            logger.info("Steam启动成功")
            send_remote_log('info', 'Steam启动成功', {
                'function': '启动Steam',
                'steam_path': self.steam_path
            }, user_code)
            return {"status": "success", "message": "Steam启动成功"}

        except Exception as e:
            logger.error(f"启动Steam失败: {str(e)}")
            send_remote_log('error', 'Steam启动异常', {
                'function': '启动Steam',
                'error': str(e)
            }, user_code)
            return {"status": "error", "message": "启动Steam失败"}
    
    def 结束Steam(self):
        """结束Steam所有进程 - 使用安全方法"""
        user_code = get_current_user_code()
        send_remote_log('info', 'Starting Steam termination process', {
            'function': 'terminate_steam',
            'stage': 'start'
        }, user_code)

        try:
            # 使用安全的Steam终止方法
            result = self._safe_terminate_steam_processes()

            if result['status'] == 'success':
                send_remote_log('info', 'Steam termination successful', {
                    'function': 'terminate_steam',
                    'method': result.get('method', 'unknown'),
                    'terminated_count': result.get('terminated_count', 0)
                }, user_code)
                return {"status": "success", "message": f"成功结束Steam进程 (方法: {result.get('method', 'unknown')})"}
            elif result['status'] == 'partial_failure':
                send_remote_log('warning', 'Steam termination partial failure', {
                    'function': 'terminate_steam',
                    'result': result
                }, user_code)
                return {"status": "warning", "message": "部分Steam进程可能仍在运行"}
            else:
                send_remote_log('error', 'Steam termination failed', {
                    'function': 'terminate_steam',
                    'result': result
                }, user_code)
                return {"status": "error", "message": "结束Steam进程失败"}

        except Exception as e:
            send_remote_log('error', 'Steam termination exception', {
                'function': 'terminate_steam',
                'error': str(e),
                'error_type': type(e).__name__
            }, user_code)
            logger.error(f"结束Steam进程异常: {str(e)}")
            return {"status": "error", "message": "结束Steam进程异常"}

    def _safe_terminate_steam_processes(self):
        """
        安全终止Steam进程的内部方法
        """
        user_code = get_current_user_code()

        try:
            send_remote_log('info', 'Attempting safe Steam termination', {
                'method': 'safe_termination',
                'stage': 'start'
            }, user_code)

            # 方法1: 尝试使用psutil（带严格异常处理）
            try:
                send_remote_log('info', 'Trying psutil method for Steam termination', {
                    'method': 'psutil'
                }, user_code)

                steam_processes = []
                terminated_count = 0

                # 安全地枚举进程
                for proc in psutil.process_iter(['pid', 'name']):
                    try:
                        if proc.info and proc.info.get('name'):
                            proc_name = proc.info['name'].lower()
                            if 'steam' in proc_name:
                                steam_processes.append(proc)
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess, AttributeError):
                        continue
                    except Exception as e:
                        send_remote_log('warning', 'Process enumeration error in Steam termination', {
                            'error': str(e),
                            'error_type': type(e).__name__
                        }, user_code)
                        continue

                if not steam_processes:
                    send_remote_log('info', 'No Steam processes found', {
                        'method': 'psutil'
                    }, user_code)
                    return {
                        'status': 'success',
                        'method': 'psutil',
                        'terminated_count': 0,
                        'message': 'no_processes_found'
                    }

                send_remote_log('info', 'Found Steam processes for termination', {
                    'count': len(steam_processes),
                    'method': 'psutil',
                    'process_names': [proc.info.get('name', 'unknown') for proc in steam_processes]
                }, user_code)

                # 终止进程
                for proc in steam_processes:
                    try:
                        proc.terminate()
                        terminated_count += 1
                        logger.info(f"Terminated Steam process: {proc.info.get('name', 'unknown')} (PID: {proc.info.get('pid', 'unknown')})")
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        continue
                    except Exception as e:
                        send_remote_log('warning', 'Process termination error', {
                            'error': str(e),
                            'pid': getattr(proc, 'pid', 'unknown')
                        }, user_code)
                        continue

                # 等待并强制杀死
                import time
                time.sleep(2)
                for proc in steam_processes:
                    try:
                        if proc.is_running():
                            proc.kill()
                            logger.info(f"Force killed Steam process: {proc.info.get('name', 'unknown')}")
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        continue
                    except Exception:
                        continue

                send_remote_log('info', 'Steam processes terminated successfully via psutil', {
                    'method': 'psutil',
                    'terminated_count': terminated_count,
                    'total_found': len(steam_processes)
                }, user_code)

                return {
                    'status': 'success',
                    'method': 'psutil',
                    'terminated_count': terminated_count,
                    'total_found': len(steam_processes)
                }

            except Exception as psutil_error:
                send_remote_log('warning', 'Psutil method failed in Steam termination, trying taskkill', {
                    'psutil_error': str(psutil_error),
                    'error_type': type(psutil_error).__name__
                }, user_code)

                # 方法2: 使用Windows taskkill命令
                try:
                    send_remote_log('info', 'Trying taskkill method for Steam termination', {
                        'method': 'taskkill'
                    }, user_code)

                    import subprocess

                    steam_processes = ['steam.exe', 'steamwebhelper.exe', 'steamservice.exe']
                    terminated_count = 0

                    for process_name in steam_processes:
                        try:
                            result = subprocess.run(
                                ['taskkill', '/F', '/IM', process_name],
                                capture_output=True,
                                text=True,
                                timeout=10,
                                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                            )

                            if result.returncode == 0:
                                terminated_count += 1
                                logger.info(f"Terminated Steam process via taskkill: {process_name}")
                                send_remote_log('info', 'Process terminated via taskkill', {
                                    'process': process_name
                                }, user_code)

                        except subprocess.TimeoutExpired:
                            send_remote_log('warning', 'Taskkill timeout for Steam process', {
                                'process': process_name
                            }, user_code)
                            continue
                        except Exception as e:
                            send_remote_log('warning', 'Taskkill error for Steam process', {
                                'process': process_name,
                                'error': str(e)
                            }, user_code)
                            continue

                    send_remote_log('info', 'Steam processes terminated via taskkill', {
                        'method': 'taskkill',
                        'terminated_count': terminated_count,
                        'total_attempted': len(steam_processes)
                    }, user_code)

                    return {
                        'status': 'success',
                        'method': 'taskkill',
                        'terminated_count': terminated_count,
                        'total_attempted': len(steam_processes)
                    }

                except Exception as taskkill_error:
                    send_remote_log('error', 'All Steam termination methods failed', {
                        'psutil_error': str(psutil_error),
                        'taskkill_error': str(taskkill_error)
                    }, user_code)

                    return {
                        'status': 'partial_failure',
                        'method': 'none',
                        'psutil_error': str(psutil_error),
                        'taskkill_error': str(taskkill_error)
                    }

        except Exception as e:
            send_remote_log('error', 'Critical error in safe Steam termination', {
                'error': str(e),
                'error_type': type(e).__name__
            }, user_code)

            return {
                'status': 'error',
                'method': 'none',
                'error': str(e)
            }
    
    def 安装Steam(self):
        """打开Steam安装包"""
        try:
            steam_installer = 获取Steam安装包路径()

            if not os.path.exists(steam_installer):
                return {"status": "error", "message": "Steam安装包不存在"}

            # 启动安装包
            subprocess.Popen([steam_installer], shell=True)
            logger.info("Steam安装包已启动")
            return {"status": "success", "message": "Steam安装包已启动，请按照向导完成安装"}

        except Exception as e:
            logger.error(f"启动Steam安装包失败: {str(e)}")
            return {"status": "error", "message": "启动Steam安装包失败"}
    
    def 打开Steam官网(self):
        """打开Steam官网"""
        try:
            webbrowser.open("https://store.steampowered.com/")
            logger.info("Steam官网已在浏览器中打开")
            return {"status": "success", "message": "Steam官网已在浏览器中打开"}
            
        except Exception as e:
            logger.error(f"打开Steam官网失败: {str(e)}")
            return {"status": "error", "message": "打开Steam官网失败"}
    
    def 彻底卸载Steam(self):
        """打开Geek Uninstaller进行彻底卸载"""
        try:
            geek_uninstaller = os.path.join(self.安装包目录, "Geek Uninstaller.exe")
            
            if not os.path.exists(geek_uninstaller):
                return {"status": "error", "message": "Geek Uninstaller不存在"}
            
            # 启动Geek Uninstaller
            subprocess.Popen([geek_uninstaller], shell=True)
            logger.info("Geek Uninstaller已启动")
            return {"status": "success", "message": "Geek Uninstaller已启动，请选择Steam进行卸载"}
            
        except Exception as e:
            logger.error(f"启动Geek Uninstaller失败: {str(e)}")
            return {"status": "error", "message": "启动Geek Uninstaller失败"}
    
    def 打开Steam配置目录(self):
        """打开Steam配置目录"""
        try:
            if not self.获取Steam路径():
                return {"status": "error", "message": "未检测到Steam安装路径"}
            
            config_dir = os.path.join(self.steam_path, "config")
            
            if not os.path.exists(config_dir):
                return {"status": "error", "message": "Steam配置目录不存在"}
            
            # 在文件管理器中打开配置目录
            if os.name == 'nt':  # Windows
                os.startfile(config_dir)
            else:  # Linux/Mac
                subprocess.Popen(['xdg-open', config_dir])
            
            logger.info(f"Steam配置目录已打开: {config_dir}")
            return {"status": "success", "message": f"Steam配置目录已打开: {config_dir}"}
            
        except Exception as e:
            logger.error(f"打开Steam配置目录失败: {str(e)}")
            return {"status": "error", "message": "打开Steam配置目录失败"}
    
    def _检查Steam进程(self):
        """检查Steam是否在运行 - 使用安全方法"""
        user_code = get_current_user_code()

        try:
            send_remote_log('info', 'Checking Steam processes safely', {
                'function': 'check_steam_process',
                'stage': 'start'
            }, user_code)

            # 方法1: 尝试使用psutil
            try:
                for proc in psutil.process_iter(['name']):
                    try:
                        if proc.info and proc.info.get('name'):
                            proc_name = proc.info['name'].lower()
                            if 'steam.exe' in proc_name:
                                send_remote_log('info', 'Steam process found via psutil', {
                                    'process_name': proc.info['name']
                                }, user_code)
                                return True
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess, AttributeError):
                        continue
                    except Exception as e:
                        send_remote_log('warning', 'Error checking individual process', {
                            'error': str(e),
                            'error_type': type(e).__name__
                        }, user_code)
                        continue

                send_remote_log('info', 'No Steam process found via psutil', {}, user_code)
                return False

            except Exception as psutil_error:
                send_remote_log('warning', 'Psutil check failed, trying tasklist', {
                    'psutil_error': str(psutil_error),
                    'error_type': type(psutil_error).__name__
                }, user_code)

                # 方法2: 使用Windows tasklist命令
                try:
                    import subprocess
                    result = subprocess.run(
                        ['tasklist', '/FI', 'IMAGENAME eq steam.exe'],
                        capture_output=True,
                        text=True,
                        timeout=10,
                        creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                    )

                    if result.returncode == 0 and 'steam.exe' in result.stdout.lower():
                        send_remote_log('info', 'Steam process found via tasklist', {}, user_code)
                        return True
                    else:
                        send_remote_log('info', 'No Steam process found via tasklist', {}, user_code)
                        return False

                except Exception as tasklist_error:
                    send_remote_log('error', 'All Steam check methods failed', {
                        'psutil_error': str(psutil_error),
                        'tasklist_error': str(tasklist_error)
                    }, user_code)
                    return False

        except Exception as e:
            send_remote_log('error', 'Critical error in Steam process check', {
                'error': str(e),
                'error_type': type(e).__name__
            }, user_code)
            return False

    def 一键还原Steam(self):
        """一键还原Steam到未入库状态"""
        user_code = get_current_user_code()
        send_remote_log('info', '执行系统维护操作', {
            'function': '系统维护'
        }, user_code)

        try:
            if not self.获取Steam路径():
                return {"status": "error", "message": "系统检测失败"}

            # 检查Steam是否在运行，如果在运行则先结束
            send_remote_log('info', 'Checking if Steam is running before cleanup', {
                'function': 'steam_cleanup'
            }, user_code)

            if self._检查Steam进程():
                send_remote_log('info', 'Steam is running, terminating before cleanup', {
                    'function': 'steam_cleanup'
                }, user_code)

                termination_result = self.结束Steam()

                send_remote_log('info', 'Steam termination result for cleanup', {
                    'function': 'steam_cleanup',
                    'termination_result': termination_result
                }, user_code)

                # 等待进程完全结束
                import time
                time.sleep(2)
            else:
                send_remote_log('info', 'Steam not running, proceeding with cleanup', {
                    'function': 'steam_cleanup'
                }, user_code)

            success_count = 0
            error_count = 0

            # 1. 删除 config/stplug-in 目录下的所有文件
            stplug_in_dir = os.path.join(self.steam_path, "config", "stplug-in")
            if os.path.exists(stplug_in_dir):
                try:
                    for item in os.listdir(stplug_in_dir):
                        item_path = os.path.join(stplug_in_dir, item)
                        if os.path.isfile(item_path):
                            os.remove(item_path)
                            success_count += 1
                        elif os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                            success_count += 1
                except Exception:
                    error_count += 1

            # 2. 删除 config/depotcache 目录下的所有文件
            depotcache_dir = os.path.join(self.steam_path, "config", "depotcache")
            if os.path.exists(depotcache_dir):
                try:
                    for item in os.listdir(depotcache_dir):
                        item_path = os.path.join(depotcache_dir, item)
                        if os.path.isfile(item_path):
                            os.remove(item_path)
                            success_count += 1
                        elif os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                            success_count += 1
                except Exception:
                    error_count += 1

            # 3. 删除 hid.dll 文件
            hid_dll_path = os.path.join(self.steam_path, "hid.dll")
            if os.path.exists(hid_dll_path):
                try:
                    os.remove(hid_dll_path)
                    success_count += 1
                except Exception:
                    error_count += 1

            # 生成结果消息
            if error_count > 0:
                send_remote_log('info', '系统维护部分完成', {
                    'function': '系统维护'
                }, user_code)
                return {"status": "warning", "message": "操作部分完成"}
            else:
                send_remote_log('info', '系统维护完成', {
                    'function': '系统维护'
                }, user_code)
                return {"status": "success", "message": "操作完成"}

        except Exception as e:
            send_remote_log('error', '系统维护异常', {
                'function': '系统维护'
            }, user_code)
            return {"status": "error", "message": "操作失败"}


# 创建全局实例
steam_manager = Steam功能管理器()
